'use client';

import React from 'react';
import {
  ReadingProgress,
  ArticleInteractions,
  FocusMode,
  TableOfContents,
} from '@/components/blog';
import { BlogPost } from '@/types';

interface BlogPostClientProps {
  post: BlogPost;
  siteUrl: string;
  locale: string;
}

export function BlogPostClient({ 
  post 
}: BlogPostClientProps) {
  return (
    <>
      {/* 阅读进度指示器 */}
      <ReadingProgress target="article" variant="bar" />



      {/* 右侧浮动工具栏 - 仅保留核心功能 */}
      <div className="fixed right-6 top-1/2 transform -translate-y-1/2 z-40 hidden lg:block">
        <ArticleInteractions
          postId={post.id}
          initialLikes={post.likeCount}
          initialViews={post.viewCount}
          initialComments={post.commentCount}
          variant="floating"
        />
      </div>

      {/* 移动端浮动目录 - 左下角 */}
      <div className="lg:hidden">
        <TableOfContents 
          content={post.content} 
          variant="floating"
          className="fixed left-6 bottom-20 z-40"
        />
      </div>

      {/* 专注模式控制 - 右下角 */}
      <FocusMode className="fixed right-6 bottom-6 z-50" />

    </>
  );
}
