import { Metadata } from 'next';
import { getTranslations } from 'next-intl/server';
import { TableOfContents } from '@/components/blog/TableOfContents';
import { MarkdownContent } from '@/components/blog/MarkdownContent';

interface TocDemoPageProps {
  params: {
    locale: string;
  };
}

export async function generateMetadata({ params }: TocDemoPageProps): Promise<Metadata> {
  return {
    title: '目录组件演示 - Table of Contents Demo',
    description: '演示博文目录组件的功能，包括侧边栏、浮动和内联三种显示模式',
  };
}

// 演示用的长文章内容
const demoContent = `
# 目录组件演示

这是一个演示目录组件功能的页面。目录组件支持三种显示模式：侧边栏模式、浮动模式和内联模式。

## 第一章：目录功能介绍

目录组件可以自动从文章内容中提取H2-H4级别的标题，生成可点击的导航目录。

### 1.1 自动提取标题

组件会智能识别Markdown和HTML格式的内容，自动提取标题信息。

### 1.2 智能锚点生成

为每个标题自动生成唯一的ID，支持锚点导航功能。

#### 1.2.1 ID生成规则

- 转换为小写
- 移除特殊字符
- 空格替换为连字符

## 第二章：显示模式

目录组件提供三种不同的显示模式，适应不同的使用场景。

### 2.1 侧边栏模式

在桌面端，目录显示在左侧边栏，固定位置，方便用户随时查看文章结构。

### 2.2 浮动模式

在移动端，目录以浮动窗口的形式显示，可以折叠和展开，不占用过多屏幕空间。

### 2.3 内联模式

目录可以直接嵌入到文章内容中，作为文章的一部分显示。

## 第三章：交互功能

目录组件提供丰富的交互功能，提升用户阅读体验。

### 3.1 点击跳转

点击目录项可以平滑滚动到对应的标题位置。

### 3.2 当前位置高亮

根据用户的滚动位置，自动高亮当前正在阅读的章节。

### 3.3 响应式设计

在不同屏幕尺寸下，目录会自动调整显示方式和样式。

## 第四章：样式定制

目录组件支持主题切换和样式定制。

### 4.1 浅色主题

默认使用浅色主题，确保最佳的可读性。

### 4.2 深色主题

完全支持深色主题，在夜间阅读时保护用户视力。

### 4.3 自定义样式

通过className属性可以自定义目录的样式。

## 第五章：技术实现

目录组件基于React Hooks实现，具有良好的性能和用户体验。

### 5.1 状态管理

使用useState管理组件的各种状态，包括激活项、可见性等。

### 5.2 滚动监听

通过addEventListener监听页面滚动，实现实时的位置跟踪。

### 5.3 性能优化

使用useMemo缓存标题提取结果，避免不必要的重复计算。

## 结语

目录组件为长文章提供了优秀的导航体验，帮助用户快速定位和跳转到感兴趣的内容。
`;

export default async function TocDemoPage({ params }: TocDemoPageProps) {
  const t = await getTranslations({ locale: params.locale, namespace: 'blog' });

  return (
    <div className="min-h-screen bg-white dark:bg-dark-900">
      {/* 页面头部 */}
      <header className="max-w-[680px] mx-auto px-4 sm:px-6 lg:px-8 pt-16 pb-8">
        <h1 className="text-4xl md:text-5xl font-bold font-serif text-gray-900 dark:text-gray-50 mb-6 leading-tight tracking-tight">
          目录组件演示
        </h1>
        <p className="text-lg md:text-xl font-normal text-gray-600 dark:text-gray-300 mb-8 leading-relaxed italic">
          演示博文目录组件的各种功能和显示模式
        </p>
      </header>

      {/* 内联目录演示 */}
      <div className="max-w-[680px] mx-auto px-4 sm:px-6 lg:px-8 mb-8">
        <h2 className="text-2xl font-bold text-gray-900 dark:text-gray-50 mb-4">内联目录模式</h2>
        <TableOfContents content={demoContent} variant="inline" />
      </div>

      {/* 主要内容区域 - 带左侧边栏的布局 */}
      <div className="relative">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex gap-8">
            {/* 左侧边栏 - 目录 */}
            <aside className="hidden lg:block w-64 flex-shrink-0">
              <div className="mb-4">
                <h3 className="text-lg font-semibold text-gray-900 dark:text-gray-100 mb-2">侧边栏目录模式</h3>
                <p className="text-sm text-gray-600 dark:text-gray-400 mb-4">
                  固定在左侧的目录，会跟随滚动高亮当前章节
                </p>
              </div>
              <TableOfContents content={demoContent} />
            </aside>

            {/* 文章内容容器 */}
            <main className="flex-1 max-w-[680px] pb-16">
              <MarkdownContent content={demoContent} />
              
              {/* 移动端提示 */}
              <div className="lg:hidden mt-8 p-4 bg-blue-50 dark:bg-blue-900/20 rounded-lg">
                <h3 className="text-lg font-semibold text-blue-900 dark:text-blue-100 mb-2">移动端浮动目录</h3>
                <p className="text-blue-800 dark:text-blue-200 text-sm">
                  在移动设备上，目录会以浮动窗口的形式显示在左下角。滚动页面时会自动显示，点击目录图标可以展开/折叠。
                </p>
              </div>
            </main>

            {/* 右侧空白区域，保持内容居中 */}
            <div className="hidden xl:block w-64 flex-shrink-0"></div>
          </div>
        </div>
      </div>

      {/* 移动端浮动目录 */}
      <div className="lg:hidden">
        <TableOfContents 
          content={demoContent} 
          variant="floating"
          className="fixed left-6 bottom-6 z-40"
        />
      </div>
    </div>
  );
}